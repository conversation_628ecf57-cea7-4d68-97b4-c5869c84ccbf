-- Drop views if they exist
IF OBJECT_ID('vwlearningassignmentcorrelation', 'V') IS NOT NULL
    DROP VIEW vwlearningassignmentcorrelation;

IF OBJECT_ID('vwlearningmodulecompletionanalytics', 'V') IS NOT NULL
    DROP VIEW vwlearningmodulecompletionanalytics;

IF OBJECT_ID('vwlearninguserassignmentsummary', 'V') IS NOT NULL
    DROP VIEW vwlearninguserassignmentsummary;

-- Create table if not exists
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name = 'learningmodules' AND xtype = 'U')
BEGIN
    CREATE TABLE learningmodules (
        id VARCHAR(50) NOT NULL PRIMARY KEY,
        name VARCHAR(100),
        description VARCHAR(512),
        version VARCHAR(255),
        externalId VARCHAR(50),
        source VARCHAR(50),
        enforceContentOrder BIT,
        isArchived BIT,
        isPublished BIT,
        completionTimeInDays INT,
        type VARCHAR(50),
        dateCreated DATETIME,
        dateModified DATETIME,
        lengthInMinutes DECIMAL(20, 2),
        state VARCHAR(50),
        updated DATETIME
    );
END

-- Add column if not exists
IF COL_LENGTH('learningmodules', 'state') IS NULL
BEGIN
    ALTER TABLE learningmodules ADD state VARCHAR(50);
END

-- Alter description column
ALTER TABLE learningmodules
ALTER COLUMN description VARCHAR(512);
