-- PostgreSQL: Remove duplicate/invalid rows from ODContactListData
-- Only runs for versions < 3.48.7 but within 3.48.% range
-- This script is automatically executed during installation/upgrade

-- Delete rows from ODContactListData for versions < 3.48.7 but is 3.48.%
WITH version_check AS (
    SELECT version
    FROM tabledefinitions
    WHERE tablename = 'odcontactlistdata'
      AND version LIKE '3.48.%'
      AND version < '********'
),
duplicates AS (
    SELECT "inin-outbound-id"
    FROM odcontactlistdata
    GROUP BY "inin-outbound-id"
    HAVING COUNT(DISTINCT contactlistid) > 1
),
ranked_rows AS (
    SELECT o.*,
           ROW_NUMBER() OVER (
               PARTITION BY o."inin-outbound-id"
               ORDER BY o.updated ASC
           ) AS rn
    FROM odcontactlistdata o
    WHERE o."inin-outbound-id" IN (SELECT "inin-outbound-id" FROM duplicates)
)
DELETE
FROM ranked_rows r
JOIN version_check v ON 1=1
WHERE r.rn = 1
ORDER BY r."inin-outbound-id";
