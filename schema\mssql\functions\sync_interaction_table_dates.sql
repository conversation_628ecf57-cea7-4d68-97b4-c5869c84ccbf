-- MSSQL: Update convsummarydata datekeyfield in tabledefinitions
-- Only runs for versions < 3.48.6 but within 3.48.% range
-- This script is automatically executed during installation/upgrade

-- Update convsummarydata datekeyfield for versions < 3.48.6 but is 3.48.%
UPDATE tabledefinitions
SET datekeyfield = '2025-06-23 00:00:00'
WHERE tablename IN ('convsummarydata','detailedinteractiondata')
AND version LIKE '3.48.%'
AND version < '********';

DELETE FROM detailedinteractiondata
WHERE conversationstartdate > '2025-06-23 00:00:00'
  AND EXISTS (
    SELECT 1 FROM tabledefinitions td
    WHERE 'detailedinteractiondata' = td.tablename
      AND td.version LIKE '3.48.%'
      AND td.version < '********'
  );
