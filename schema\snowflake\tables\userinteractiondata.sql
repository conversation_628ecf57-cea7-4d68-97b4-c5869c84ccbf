CREATE TABLE IF NOT EXISTS userinteractiondata (
    keyid varchar(255) NOT NULL,
    userid varchar(50),
    direction varchar(50),
    queueid varchar(50),
    mediatype varchar(50),
    wrapupcode varchar(255),
    startdate timestamp without time zone NOT NULL,
    startdateltc timestamp without time zone NOT NULL,
    talertcount integer,
    talerttimesum numeric(20, 2),
    talerttimemax numeric(20, 2),
    talerttimemin numeric(20, 2),
    tansweredcount integer,
    tansweredtimesum numeric(20, 2),
    tansweredtimemax numeric(20, 2),
    tansweredtimemin numeric(20, 2),
    ttalkcount integer,
    ttalktimesum numeric(20, 2),
    ttalktimemax numeric(20, 2),
    ttalktimemin numeric(20, 2),
    ttalkcompletecount integer,
    ttalkcompletetimesum numeric(20, 2),
    ttalkcompletetimemax numeric(20, 2),
    ttalkcompletetimemin numeric(20, 2),
    tnotrespondingcount integer,
    tnotrespondingtimesum numeric(20, 2),
    tnotrespondingtimemax numeric(20, 2),
    tnotrespondingtimemin numeric(20, 2),
    theldcount integer,
    theldtimesum numeric(20, 2),
    theldtimemax numeric(20, 2),
    theldtimemin numeric(20, 2),
    theldcompletecount integer,
    theldcompletetimesum numeric(20, 2),
    theldcompletetimemax numeric(20, 2),
    theldcompletetimemin numeric(20, 2),
    thandlecount integer,
    thandletimesum numeric(20, 2),
    thandletimemax numeric(20, 2),
    thandletimemin numeric(20, 2),
    tacwcount integer,
    tacwtimesum numeric(20, 2),
    tacwtimemax numeric(20, 2),
    tacwtimemin numeric(20, 2),
    nconsult integer,
    nconsulttransferred integer,
    noutbound integer,
    nerror integer,
    ntransferred integer,
    nblindtransferred integer,
    nconnected integer,
    tdialingcount integer,
    tdialingtimesum numeric(20, 2),
    tdialingtimemax numeric(20, 2),
    tdialingtimemin numeric(20, 2),
    tcontactingcount integer,
    tcontactingtimesum numeric(20, 2),
    tcontactingtimemax numeric(20, 2),
    tcontactingtimemin numeric(20, 2),
    tvoicemailcount integer,
    tvoicemailtimesum numeric(20, 2),
    tvoicemailtimemax numeric(20, 2),
    tvoicemailtimemin numeric(20, 2),
    tuserresponsetimecount integer,
    tuserresponsetimetimesum numeric(20, 2),
    tuserresponsetimetimemax numeric(20, 2),
    tuserresponsetimetimemin numeric(20, 2),
    tagentresponsetimecount integer,
    tagentresponsetimetimesum numeric(20, 2),
    tagentresponsetimetimemax numeric(20, 2),
    tagentresponsetimetimemin numeric(20, 2),
    av1count integer,
    av1timesum numeric(20, 2),
    av1timemax numeric(20, 2),
    av1timemin numeric(20, 2),
    av2count integer,
    av2timesum numeric(20, 2),
    av2timemax numeric(20, 2),
    av2timemin numeric(20, 2),
    av3count integer,
    av3timesum numeric(20, 2),
    av3timemax numeric(20, 2),
    av3timemin numeric(20, 2),
    av4count integer,
    av4timesum numeric(20, 2),
    av4timemax numeric(20, 2),
    av4timemin numeric(20, 2),
    av5count integer,
    av5timesum numeric(20, 2),
    av5timemax numeric(20, 2),
    av5timemin numeric(20, 2),
    av6count integer,
    av6timesum numeric(20, 2),
    av6timemax numeric(20, 2),
    av6timemin numeric(20, 2),
    av7count integer,
    av7timesum numeric(20, 2),
    av7timemax numeric(20, 2),
    av7timemin numeric(20, 2),
    av8count integer,
    av8timesum numeric(20, 2),
    av8timemax numeric(20, 2),
    av8timemin numeric(20, 2),
    av9count integer,
    av9timesum numeric(20, 2),
    av9timemax numeric(20, 2),
    av9timemin numeric(20, 2),
    av10count integer,
    av10timesum numeric(20, 2),
    av10timemax numeric(20, 2),
    av10timemin numeric(20, 2),
    noutboundabandoned integer,
    noutboundattempted integer,
    noutboundconnected integer,
    nstatetransitionerror integer,
    oexternalmediacount integer,
    tmonitoringcount integer,
    tmonitoringtimesum numeric(20, 2),
    tmonitoringtimemax numeric(20, 2),
    tmonitoringtimemin numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userinteractiondata_pkey PRIMARY KEY (keyid, startdate)
);