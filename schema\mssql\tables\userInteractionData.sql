IF dbo.csg_table_exists('userInteractionData') = 0
CREATE TABLE [userInteractionData](
    [keyid] [nvarchar](255) NOT NULL,
    [userid] [nvarchar](50),
    [direction] [nvarchar](50),
    [queueid] [nvarchar](50),
    [mediatype] [nvarchar](50),
    [wrapupcode] [nvarchar](255),
    [startdate] [datetime],
    [startdateltc] [datetime],
    [talertcount] [int],
    [talerttimesum] [decimal](20, 2),
    [talerttimemax] [decimal](20, 2),
    [talerttimemin] [decimal](20, 2),
    [tansweredcount] [int],
    [tansweredtimesum] [decimal](20, 2),
    [tansweredtimemax] [decimal](20, 2),
    [tansweredtimemin] [decimal](20, 2),
    [ttalkcount] [int],
    [ttalktimesum] [decimal](20, 2),
    [ttalktimemax] [decimal](20, 2),
    [ttalktimemin] [decimal](20, 2),
    [ttalkcompletecount] [int],
    [ttalkcompletetimesum] [decimal](20, 2),
    [ttalkcompletetimemax] [decimal](20, 2),
    [ttalkcompletetimemin] [decimal](20, 2),
    [tnotrespondingcount] [int],
    [tnotrespondingtimesum] [decimal](20, 2),
    [tnotrespondingtimemax] [decimal](20, 2),
    [tnotrespondingtimemin] [decimal](20, 2),
    [theldcount] [int],
    [theldtimesum] [decimal](20, 2),
    [theldtimemax] [decimal](20, 2),
    [theldtimemin] [decimal](20, 2),
    [theldcompletecount] [int],
    [theldcompletetimesum] [decimal](20, 2),
    [theldcompletetimemax] [decimal](20, 2),
    [theldcompletetimemin] [decimal](20, 2),
    [thandlecount] [int],
    [thandletimesum] [decimal](20, 2),
    [thandletimemax] [decimal](20, 2),
    [thandletimemin] [decimal](20, 2),
    [tacwcount] [int],
    [tacwtimesum] [decimal](20, 2),
    [tacwtimemax] [decimal](20, 2),
    [tacwtimemin] [decimal](20, 2),
    [nconsult] [int],
    [nconsulttransferred] [int],
    [noutbound] [int],
    [nerror] [int],
    [ntransferred] [int],
    [nblindtransferred] [int],
    [nconnected] [int],
    [tdialingcount] [int],
    [tdialingtimesum] [decimal](20, 2),
    [tdialingtimemax] [decimal](20, 2),
    [tdialingtimemin] [decimal](20, 2),
    [tcontactingcount] [int],
    [tcontactingtimesum] [decimal](20, 2),
    [tcontactingtimemax] [decimal](20, 2),
    [tcontactingtimemin] [decimal](20, 2),
    [tvoicemailcount] [int],
    [tvoicemailtimesum] [decimal](20, 2),
    [tvoicemailtimemax] [decimal](20, 2),
    [tvoicemailtimemin] [decimal](20, 2),
    [tuserresponsetimecount] [int],
    [tuserresponsetimetimesum] [decimal](20, 2),
    [tuserresponsetimetimemx] [decimal](20, 2),
    [tuserresponsetimetimemin] [decimal](20, 2),
    [tagentresponsetimecount] [int],
    [tagentresponsetimetimesum] [decimal](20, 2),
    [tagentresponsetimetimemax] [decimal](20, 2),
    [tagentresponsetimetimemin] [decimal](20, 2),
    [av1count] [int],
    [av1timesum] [decimal](20, 2),
    [av1timemax] [decimal](20, 2),
    [av1timemin] [decimal](20, 2),
    [av2count] [int],
    [av2timesum] [decimal](20, 2),
    [av2timemax] [decimal](20, 2),
    [av2timemin] [decimal](20, 2),
    [av3count] [int],
    [av3timesum] [decimal](20, 2),
    [av3timemax] [decimal](20, 2),
    [av3timemin] [decimal](20, 2),
    [av4count] [int],
    [av4timesum] [decimal](20, 2),
    [av4timemax] [decimal](20, 2),
    [av4timemin] [decimal](20, 2),
    [av5count] [int],
    [av5timesum] [decimal](20, 2),
    [av5timemax] [decimal](20, 2),
    [av5timemin] [decimal](20, 2),
    [av6count] [int],
    [av6timesum] [decimal](20, 2),
    [av6timemax] [decimal](20, 2),
    [av6timemin] [decimal](20, 2),
    [av7count] [int],
    [av7timesum] [decimal](20, 2),
    [av7timemax] [decimal](20, 2),
    [av7timemin] [decimal](20, 2),
    [av8count] [int],
    [av8timesum] [decimal](20, 2),
    [av8timemax] [decimal](20, 2),
    [av8timemin] [decimal](20, 2),
    [av9count] [int],
    [av9timesum] [decimal](20, 2),
    [av9timemax] [decimal](20, 2),
    [av9timemin] [decimal](20, 2),
    [av10count] [int],
    [av10timesum] [decimal](20, 2),
    [av10timemax] [decimal](20, 2),
    [av10timemin] [decimal](20, 2),
    [noutboundabandoned] [int],
    [noutboundattempted] [int],
    [noutboundconnected] [int],
    [nstatetransitionerror] [int],
    [oexternalmediacount] [int],
    [tmonitoringcount] [int],
    [tmonitoringtimesum] [decimal](20, 2),
    [tmonitoringtimemax] [decimal](20, 2),
    [tmonitoringtimemin] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_userInteractionData] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('UserInteractionDataQueue', 'userInteractionData') = 0
CREATE INDEX [UserInteractionDataQueue] ON [userInteractionData] ([queueid]);
IF dbo.csg_index_exists('UserInteractionDataStartDate', 'userInteractionData') = 0
CREATE INDEX [UserInteractionDataStartDate] ON [userInteractionData] ([startdate]);
IF dbo.csg_index_exists('UserInteractionDataStartDateLTC', 'userInteractionData') = 0
CREATE INDEX [UserInteractionDataStartDateLTC] ON [userInteractionData] ([startdateltc]);
IF dbo.csg_index_exists('UserInteractionDataUser', 'userInteractionData') = 0
CREATE INDEX [UserInteractionDataUser] ON [userInteractionData] ([userid]);
IF dbo.csg_index_exists('UserIntreactionDataMedia', 'userInteractionData') = 0
CREATE INDEX [UserIntreactionDataMedia] ON [userInteractionData] ([mediatype]);
IF dbo.csg_index_exists('UserInteractionDataWrapUp', 'userInteractionData') = 0
CREATE INDEX [UserInteractionDataWrapUp] ON [userInteractionData] ([wrapupcode]);

IF dbo.csg_column_exists('userInteractionData', 'tuserresponsetimecount') = 0
    ALTER TABLE userInteractionData ADD tuserresponsetimecount INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tuserresponsetimecount INT;

IF dbo.csg_column_exists('userInteractionData', 'tuserresponsetimetimesum') = 0
    ALTER TABLE userInteractionData ADD tuserresponsetimetimesum DECIMAL(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tuserresponsetimetimesum DECIMAL(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'tuserresponsetimetimemx') = 0
    ALTER TABLE userInteractionData ADD tuserresponsetimetimemx DECIMAL(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tuserresponsetimetimemx DECIMAL(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'tuserresponsetimetimemin') = 0
    ALTER TABLE userInteractionData ADD tuserresponsetimetimemin DECIMAL(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tuserresponsetimetimemin DECIMAL(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'tagentresponsetimecount') = 0
    ALTER TABLE userInteractionData ADD tagentresponsetimecount INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tagentresponsetimecount INT;

IF dbo.csg_column_exists('userInteractionData', 'tagentresponsetimetimesum') = 0
    ALTER TABLE userInteractionData ADD tagentresponsetimetimesum DECIMAL(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tagentresponsetimetimesum DECIMAL(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'tagentresponsetimetimemax') = 0
    ALTER TABLE userInteractionData ADD tagentresponsetimetimemax DECIMAL(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tagentresponsetimetimemax DECIMAL(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'tagentresponsetimetimemin') = 0
    ALTER TABLE userInteractionData ADD tagentresponsetimetimemin DECIMAL(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tagentresponsetimetimemin DECIMAL(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av1count') = 0
    ALTER TABLE userInteractionData ADD av1count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av1count INT;

IF dbo.csg_column_exists('userInteractionData', 'av1timesum') = 0
    ALTER TABLE userInteractionData ADD av1timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av1timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av1timemax') = 0
    ALTER TABLE userInteractionData ADD av1timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av1timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av1timemin') = 0
    ALTER TABLE userInteractionData ADD av1timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av1timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av2count') = 0
    ALTER TABLE userInteractionData ADD av2count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av2count INT;

IF dbo.csg_column_exists('userInteractionData', 'av2timesum') = 0
    ALTER TABLE userInteractionData ADD av2timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av2timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av2timemax') = 0
    ALTER TABLE userInteractionData ADD av2timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av2timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av2timemin') = 0
    ALTER TABLE userInteractionData ADD av2timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av2timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av3count') = 0
    ALTER TABLE userInteractionData ADD av3count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av3count INT;

IF dbo.csg_column_exists('userInteractionData', 'av3timesum') = 0
    ALTER TABLE userInteractionData ADD av3timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av3timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av3timemax') = 0
    ALTER TABLE userInteractionData ADD av3timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av3timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av3timemin') = 0
    ALTER TABLE userInteractionData ADD av3timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av3timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av4count') = 0
    ALTER TABLE userInteractionData ADD av4count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av4count INT;

IF dbo.csg_column_exists('userInteractionData', 'av4timesum') = 0
    ALTER TABLE userInteractionData ADD av4timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av4timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av4timemax') = 0
    ALTER TABLE userInteractionData ADD av4timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av4timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av4timemin') = 0
    ALTER TABLE userInteractionData ADD av4timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av4timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av5count') = 0
    ALTER TABLE userInteractionData ADD av5count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av5count INT;

IF dbo.csg_column_exists('userInteractionData', 'av5timesum') = 0
    ALTER TABLE userInteractionData ADD av5timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av5timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av5timemax') = 0
    ALTER TABLE userInteractionData ADD av5timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av5timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av5timemin') = 0
    ALTER TABLE userInteractionData ADD av5timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av5timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av6count') = 0
    ALTER TABLE userInteractionData ADD av6count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av6count INT;

IF dbo.csg_column_exists('userInteractionData', 'av6timesum') = 0
    ALTER TABLE userInteractionData ADD av6timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av6timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av6timemax') = 0
    ALTER TABLE userInteractionData ADD av6timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av6timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av6timemin') = 0
    ALTER TABLE userInteractionData ADD av6timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av6timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av7count') = 0
    ALTER TABLE userInteractionData ADD av7count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av7count INT;

IF dbo.csg_column_exists('userInteractionData', 'av7timesum') = 0
    ALTER TABLE userInteractionData ADD av7timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av7timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av7timemax') = 0
    ALTER TABLE userInteractionData ADD av7timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av7timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av7timemin') = 0
    ALTER TABLE userInteractionData ADD av7timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av7timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av8count') = 0
    ALTER TABLE userInteractionData ADD av8count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av8count INT;

IF dbo.csg_column_exists('userInteractionData', 'av8timesum') = 0
    ALTER TABLE userInteractionData ADD av8timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av8timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av8timemax') = 0
    ALTER TABLE userInteractionData ADD av8timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av8timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av8timemin') = 0
    ALTER TABLE userInteractionData ADD av8timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av8timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av9count') = 0
    ALTER TABLE userInteractionData ADD av9count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av9count INT;

IF dbo.csg_column_exists('userInteractionData', 'av9timesum') = 0
    ALTER TABLE userInteractionData ADD av9timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av9timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av9timemax') = 0
    ALTER TABLE userInteractionData ADD av9timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av9timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av9timemin') = 0
    ALTER TABLE userInteractionData ADD av9timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av9timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av10count') = 0
    ALTER TABLE userInteractionData ADD av10count INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av10count INT;

IF dbo.csg_column_exists('userInteractionData', 'av10timesum') = 0
    ALTER TABLE userInteractionData ADD av10timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av10timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av10timemax') = 0
    ALTER TABLE userInteractionData ADD av10timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av10timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'av10timemin') = 0
    ALTER TABLE userInteractionData ADD av10timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN av10timemin NUMERIC(20, 2);

-- Add missing user-specific metrics columns
IF dbo.csg_column_exists('userInteractionData', 'noutboundabandoned') = 0
    ALTER TABLE userInteractionData ADD noutboundabandoned INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN noutboundabandoned INT;

IF dbo.csg_column_exists('userInteractionData', 'noutboundattempted') = 0
    ALTER TABLE userInteractionData ADD noutboundattempted INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN noutboundattempted INT;

IF dbo.csg_column_exists('userInteractionData', 'noutboundconnected') = 0
    ALTER TABLE userInteractionData ADD noutboundconnected INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN noutboundconnected INT;

IF dbo.csg_column_exists('userInteractionData', 'nstatetransitionerror') = 0
    ALTER TABLE userInteractionData ADD nstatetransitionerror INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN nstatetransitionerror INT;

IF dbo.csg_column_exists('userInteractionData', 'oexternalmediacount') = 0
    ALTER TABLE userInteractionData ADD oexternalmediacount INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN oexternalmediacount INT;



IF dbo.csg_column_exists('userInteractionData', 'tmonitoringcount') = 0
    ALTER TABLE userInteractionData ADD tmonitoringcount INT;
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tmonitoringcount INT;

IF dbo.csg_column_exists('userInteractionData', 'tmonitoringtimesum') = 0
    ALTER TABLE userInteractionData ADD tmonitoringtimesum DECIMAL(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tmonitoringtimesum DECIMAL(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'tmonitoringtimemax') = 0
    ALTER TABLE userInteractionData ADD tmonitoringtimemax DECIMAL(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tmonitoringtimemax DECIMAL(20, 2);

IF dbo.csg_column_exists('userInteractionData', 'tmonitoringtimemin') = 0
    ALTER TABLE userInteractionData ADD tmonitoringtimemin DECIMAL(20, 2);
ELSE
    ALTER TABLE userInteractionData ALTER COLUMN tmonitoringtimemin DECIMAL(20, 2);



