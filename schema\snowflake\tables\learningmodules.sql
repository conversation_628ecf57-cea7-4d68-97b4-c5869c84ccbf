-- Drop views
DROP VIEW IF EXISTS vwlearningassignmentcorrelation;
DROP VIEW IF EXISTS vwlearningmodulecompletionanalytics;
DROP VIEW IF EXISTS vwlearninguserassignmentsummary;

-- Create table if not exists
CREATE TABLE IF NOT EXISTS learningmodules (
    id STRING NOT NULL,
    name STRING,
    description STRING(512),
    version STRING,
    externalId STRING,
    source STRING,
    enforceContentOrder BOOLEAN,
    isArchived BOOLEAN,
    isPublished BOOLEAN,
    completionTimeInDays INTEGER,
    type STRING,
    dateCreated TIMESTAMP_NTZ,
    dateModified TIMESTAMP_NTZ,
    lengthInMinutes NUMBER(20,2),
    state STRING,
    updated TIMESTAMP_NTZ,
    PRIMARY KEY (id)
);

-- Add column if not exists
ALTER TABLE learningmodules
ADD COLUMN IF NOT EXISTS state STRING;

-- Alter column to limit length
ALTER TABLE learningmodules
MODIFY COLUMN description STRING(512);
