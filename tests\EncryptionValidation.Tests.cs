using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Text;
using Xunit;
using Xunit.Abstractions;

namespace GenesysAdapter.UnitTests;

public class EncryptionValidation_Tests
{
    private readonly ITestOutputHelper _output;

    public EncryptionValidation_Tests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public void Program_ShouldExitWhenGenesysClientSecretNotEncrypted()
    {
        // Arrange
        var args = new string[]
        {
            "Job=Realtime",
            "GenesysApi:ClientId=test-client-id",
            "GenesysApi:ClientSecret=unencrypted-secret", // Not encrypted
            "GenesysApi:Endpoint=https://api.mypurecloud.com.au",
            "Database:Type=PostgreSQL",
            "Database:Address=localhost",
            "Database:Port=5432",
            "Database:Name=test",
            "Database:User=testuser",
            "Database:Password=enc:v2:encrypted-password-here", // Encrypted
            "Database:Schema=public"
        };

        // Act & Assert
        var exitCode = RunProgramWithArgs(args);
        
        // Should exit with code 1 due to unencrypted secret
        Assert.Equal(1, exitCode);
    }

    [Fact]
    public void Program_ShouldExitWhenDatabasePasswordNotEncrypted()
    {
        // Arrange
        var args = new string[]
        {
            "Job=Realtime",
            "GenesysApi:ClientId=test-client-id",
            "GenesysApi:ClientSecret=enc:v2:encrypted-secret-here", // Encrypted
            "GenesysApi:Endpoint=https://api.mypurecloud.com.au",
            "Database:Type=PostgreSQL",
            "Database:Address=localhost",
            "Database:Port=5432",
            "Database:Name=test",
            "Database:User=testuser",
            "Database:Password=unencrypted-password", // Not encrypted
            "Database:Schema=public"
        };

        // Act & Assert
        var exitCode = RunProgramWithArgs(args);
        
        // Should exit with code 1 due to unencrypted password
        Assert.Equal(1, exitCode);
    }

    [Fact]
    public void Program_ShouldExitWhenBothSecretsNotEncrypted()
    {
        // Arrange
        var args = new string[]
        {
            "Job=Realtime",
            "GenesysApi:ClientId=test-client-id",
            "GenesysApi:ClientSecret=unencrypted-secret", // Not encrypted
            "GenesysApi:Endpoint=https://api.mypurecloud.com.au",
            "Database:Type=PostgreSQL",
            "Database:Address=localhost",
            "Database:Port=5432",
            "Database:Name=test",
            "Database:User=testuser",
            "Database:Password=unencrypted-password", // Not encrypted
            "Database:Schema=public"
        };

        // Act & Assert
        var exitCode = RunProgramWithArgs(args);
        
        // Should exit with code 1 due to unencrypted secrets
        Assert.Equal(1, exitCode);
    }

    [Fact]
    public void Secret_IsEncrypted_ShouldReturnTrueForEncryptedV2Secret()
    {
        // Arrange
        var key = "test-key";
        var encryptedValue = "enc:v2:dGVzdC1lbmNyeXB0ZWQtdmFsdWU="; // Mock encrypted value
        
        // Act
        var secret = new StandardUtils.Secret(key, encryptedValue);
        
        // Assert
        Assert.True(secret.IsEncrypted);
        Assert.Equal(2, secret.EncryptionVersion);
    }

    [Fact]
    public void Secret_IsEncrypted_ShouldReturnFalseForPlainTextSecret()
    {
        // Arrange
        var key = "test-key";
        var plainTextValue = "plain-text-secret";
        
        // Act
        var secret = new StandardUtils.Secret(key, plainTextValue);
        
        // Assert
        Assert.False(secret.IsEncrypted);
        Assert.Equal(0, secret.EncryptionVersion);
    }

    private int RunProgramWithArgs(string[] args)
    {
        // This is a simplified test that simulates the validation logic
        // In a real test, you would need to mock the actual Program.Main execution
        // For now, we'll test the validation logic directly
        
        var options = new CSG.Adapter.Configuration.Options();
        var defaultOptions = new Dictionary<string, string?>(StringComparer.OrdinalIgnoreCase)
        {
            ["Database:Name"] = "postgres",
            ["Database:Schema"] = "public",
            ["Database:Type"] = nameof(CSG.Adapter.Configuration.DatabaseType.PostgreSQL),
            ["LogLevel"] = "Information",
            ["Preferences:Telemetry"] = "true",
            ["GenesysApi:Endpoint"] = "https://api.mypurecloud.com.au",
            ["Preferences:OffsetMonths"] = "1",
            ["Preferences:TimeZone"] = "Australia/Sydney",
            ["Preferences:MaxSyncSpan"] = "1.00:00:00",
            ["Preferences:LookBackSpan"] = "1.00:00:00"
        };

        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(defaultOptions)
            .AddCommandLine(args)
            .Build();

        try
        {
            configuration.Bind(options);
        }
        catch (InvalidOperationException)
        {
            return 1;
        }

        // Simulate the validation logic from Program.cs
        if (options.GenesysApi == null
            || options.Database == null
            || string.IsNullOrEmpty(options.GenesysApi.ClientId)
            || string.IsNullOrEmpty(options.GenesysApi.ClientSecret)
            || string.IsNullOrEmpty(options.Database.Address)
            || string.IsNullOrEmpty(options.Database.User)
            || string.IsNullOrEmpty(options.Database.Password)
            || new StandardUtils.Secret(options.GenesysApi.ClientId, options.GenesysApi.ClientSecret).IsEncrypted == false
            || new StandardUtils.Secret(options.GenesysApi.ClientId, options.Database.Password).IsEncrypted == false)
        {
            return 1; // Exit code 1 for validation failure
        }

        return 0; // Success
    }
}
