-- Drop views
DROP VIEW IF EXISTS vwlearningassignmentcorrelation CASCADE;
DROP VIEW IF EXISTS vwlearningmodulecompletionanalytics CASCADE;
DROP VIEW IF EXISTS vwlearninguserassignmentsummary CASCADE;

-- Create table if not exists
CREATE TABLE IF NOT EXISTS learningmodules (
    id VARCHAR(50) NOT NULL,
    name VA<PERSON><PERSON><PERSON>(100),
    description VARCHAR(512),
    version VARCHAR(255),
    externalId VARCHAR(50),
    source VARCHAR(50),
    enforceContentOrder BOOLEAN,
    isArchived BOOLEAN,
    isPublished BOOLEAN,
    completionTimeInDays INTEGER,
    type VARCHAR(50),
    dateCreated TIMES<PERSON>MP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes NUMERIC(20, 2),
    state VARCHAR(50),
    updated TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT learningmodule_pkey PRIMARY KEY (id)
);

-- Add column if it doesn't exist
ALTER TABLE learningmodules
ADD COLUMN IF NOT EXISTS state VARCHAR(50);

-- Alter description to varchar(512)
ALTER TABLE learningmodules
ALTER COLUMN description TYPE VARCHAR(512);
