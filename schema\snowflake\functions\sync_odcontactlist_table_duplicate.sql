-- Snow<PERSON>lake: Remove duplicate/invalid rows from ODContactListData
-- Only runs for versions < 3.48.7 but within 3.48.% range
-- This script is automatically executed during installation/upgrade

-- Delete rows from ODContactListData for versions < 3.48.7 but is 3.48.%
-- <PERSON><PERSON><PERSON> requires a different approach for DELETE with CTEs
DELETE FROM odcontactlistdata
WHERE EXISTS (
    SELECT 1
    FROM tabledefinitions
    WHERE tablename = 'odcontactlistdata'
      AND version LIKE '3.48.%'
      AND version < '********'
)
AND inin_outbound_id IN (
    SELECT inin_outbound_id
    FROM (
        SELECT inin_outbound_id,
               ROW_NUMBER() OVER (
                   PARTITION BY inin_outbound_id
                   ORDER BY updated ASC
               ) AS rn
        FROM odcontactlistdata
        WHERE inin_outbound_id IN (
            SELECT inin_outbound_id
            FROM odcontactlistdata
            GROUP BY inin_outbound_id
            HAVING COUNT(DISTINCT contactlistid) > 1
        )
    )
    WHERE rn = 1
);
