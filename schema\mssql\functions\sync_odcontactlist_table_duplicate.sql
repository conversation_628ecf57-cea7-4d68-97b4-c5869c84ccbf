-- MSSQL: Remove duplicate/invalid rows from ODContactListData
-- Only runs for versions < 3.48.7 but within 3.48.% range
-- This script is automatically executed during installation/upgrade

-- Delete rows from ODContactListData for versions < 3.48.7 but is 3.48.%
DELETE FROM odcontactlistdata
WHERE EXISTS (
    SELECT 1
    FROM tabledefinitions
    WHERE tablename = 'odcontactlistdata'
      AND version LIKE '3.48.%'
      AND version < '********'
)
AND [inin-outbound-id] IN (
    SELECT [inin-outbound-id]
    FROM (
        SELECT [inin-outbound-id],
               ROW_NUMBER() OVER (
                   PARTITION BY [inin-outbound-id]
                   ORDER BY updated ASC
               ) AS rn
        FROM odcontactlistdata
        WHERE [inin-outbound-id] IN (
            SELECT [inin-outbound-id]
            FROM odcontactlistdata
            GROUP BY [inin-outbound-id]
            HAVING COUNT(DISTINCT contactlistid) > 1
        )
    ) ranked_rows
    WHERE rn = 1
);
