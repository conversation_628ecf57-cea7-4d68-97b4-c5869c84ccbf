using System.Data;
using CSG.Adapter.Configuration;
using Microsoft.Extensions.Logging;
using StandardUtils;
using GCData;

namespace GenesysAdapter
{
    class GCUpdateFactTables
    {
        private const string PERMISSION_DENIED_TEXT = "Permission denied";

        public string CustomerKeyID { get; set; }
        private DBUtils.DBUtils DBAdapter = new();
        private GCFactData.GCFactData GCFact;
        private readonly ILogger _logger;

        public GCUpdateFactTables(ILogger logger)
        {
            _logger = logger;
            GCFact = new GCFactData.GCFactData(logger);
            CustomerKeyID = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_CUSTOMERKEYID");
        }

        public async Task<Boolean> UpdateGCFactData(FactDataJob[] FactDataJobs)
        {
            Boolean Successful = false;

            DBAdapter.Initialize();
            GCFact.Initialize();

            _logger.LogInformation("Control Table has {0} Rows", DBAdapter.ControlData.Rows.Count);

            _logger.LogInformation("Fact data jobs configured: {0}", FactDataJobs);
            foreach (var job in FactDataJobs)
            {
                _logger.LogInformation("Running fact data job: {0}", job);
                switch (job)
                {
                    case FactDataJob.All:
                    case FactDataJob.ActivityCodeDetails:
                        if (!ActivityCodeDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.BUDetails;

                    case FactDataJob.BUDetails:
                        if (!BUDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.DivisionDetails;

                    case FactDataJob.DivisionDetails:
                        if (!DivisionDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.EvaluationDetails;

                    case FactDataJob.EvaluationDetails:
                    case FactDataJob.EvaluationsDetails:
                        if (!EvaluationDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.GroupDetails;

                    case FactDataJob.GroupDetails:
                        if (!Groups())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.MUDetails;

                    case FactDataJob.MUDetails:
                        if (!MUDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.PlanningGroupDetails;

                    case FactDataJob.PlanningGroupDetails:
                        if (!PlanningGroupDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.ServiceGoalDetails;

                    case FactDataJob.ServiceGoalDetails:
                        if (!ServiceGoalDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.PresenceDetails;

                    case FactDataJob.PresenceDetails:
                        if (!PresenceDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.QueueDetails;

                    case FactDataJob.QueueDetails:
                        if (!QueueDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.SkillDetails;

                    case FactDataJob.SkillDetails:
                        if (!await SkillDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.TeamDetails;

                    case FactDataJob.TeamDetails:
                    case FactDataJob.TeamsDetails:
                        if (!TeamsDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.UserDetails;

                    case FactDataJob.UserDetails:
                        if (!await UserDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.WrapupDetails;

                    case FactDataJob.WrapupDetails:
                        if (!WrapDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.LearningDataDetails;
                    case FactDataJob.LearningDataDetails:
                        if (!LearningDataDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.ODDetails;
                    case FactDataJob.ODDetails:
                        if (!UpdateODDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.KnowledgeBaseDetails;
                    case FactDataJob.KnowledgeBaseDetails:
                        if (!KnowledgeBaseDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        if (job != FactDataJob.All) break; else goto case FactDataJob.FlowOutcomeDetails;
                    case FactDataJob.FlowOutcomeDetails:
                        Successful = FlowOutcomeDetails();
                        if (!Successful) _logger.LogError("Failed sync of fact data {0}", job);
                        if (job != FactDataJob.All) break; else goto case FactDataJob.ScheduleDetails;
                    case FactDataJob.ScheduleDetails:
                        if (!ScheduleDetails())
                        {
                            _logger.LogError("Failed sync of fact data {0}", job);
                            Successful = false;
                        }
                        break;

                    default:
                        throw new NotImplementedException(string.Format("Fact data job {0} is not implemented", job));
                }
            }
            return Successful;
        }

        private async Task<bool> SkillDetails()
        {
            bool Successful = true;

            DataTable Users = await GCFact.UserDetails();

            if (Users != null && Users.Rows.Count > 0 )
            {
                DataTable SkillCodes = await GCFact.SkillDetails();

                if (SkillCodes != null && SkillCodes.Rows.Count > 0)
                {
                    Successful = DBAdapter.WriteSQLData(SkillCodes, "skillDetails");
                    UpdateLastRefreshed(Successful, "skilldetails");
                }
                else
                    _logger.LogInformation("Skills Not Required or Found");

                if (SkillCodes != null && SkillCodes.Rows.Count > 0 && Users != null && Users.Rows.Count > 0)
                {
                    DataTable SkillsMapping = await GCFact.SkillsMapping(Users);

                    if (SkillsMapping != null && SkillsMapping.Rows.Count > 0)
                    {
                        Successful = DBAdapter.WriteSQLData(SkillsMapping, "userskillMappings");
                        UpdateLastRefreshed(Successful, "userskillmappings");
                    }

                }
            }

            return Successful;
        }

         public bool TeamsDetails()
        {
            DBUtils.DBUtils db = new();
            db.Initialize();

            int numAdded = 0;
            int numRemoved = 0;
            int numUpdated = 0;

            // Process teams, team members are retrieved later.

            // Load teams from database
            using DataTable? teamDetails = db.GetSQLTableData(
                "SELECT * FROM " + nameof(teamDetails),
                nameof(teamDetails)
            );
            if (teamDetails == null)
                throw new DataException($"Failed to retrieve the {nameof(teamDetails)} table from the database");

            // Get teams from Genesys
            DataTable TeamsData = GCFact.TeamDetails();

            // Populate a dictionary of keys for quick comparison
            System.Collections.Generic.Dictionary<string, DataRow> dbKeyToRow = new();
            System.Collections.Generic.Dictionary<string, DataRow> gcKeyToRow = new();
            foreach(DataRow row in teamDetails.Rows)
            {
                string? rowId = row.Field<string>("id");
                if (string.IsNullOrEmpty(rowId))
                    throw new InvalidDataException($"Field 'id' in table {nameof(teamDetails)} is null or empty");
                dbKeyToRow.Add(rowId, row);
            }
            // Loop through all records from Genesys
            foreach(DataRow row in TeamsData.Rows)
            {
                // If the database is missing this row, add it to the database.
                string? rowId = row.Field<string>("id");
                if (string.IsNullOrEmpty(rowId))
                    throw new InvalidDataException($"Field 'id' from Genesys is null or empty for table {nameof(teamDetails)}");

                  gcKeyToRow.Add(rowId, row);
                if (! dbKeyToRow.ContainsKey(rowId))
                {
                    teamDetails.ImportRow(row);
                    numAdded++;

                }
                else             // Update Record if exists
                {
                    dbKeyToRow[rowId]["name"]= row["name"];
                    dbKeyToRow[rowId]["division"] = row["division"];
                    dbKeyToRow[rowId]["membercount"] = row["membercount"];
                     dbKeyToRow[rowId]["updated"] = row["updated"];
                    numUpdated++;
                }
            }
            // Loop through all records from the database
            foreach(string keyid in dbKeyToRow.Keys)
            {
                // If Genesys is missing this row, delete it from the database.
                if (! gcKeyToRow.ContainsKey(keyid))
                {
                    dbKeyToRow[keyid].Delete();
                    numRemoved++;
                }
            }

            _logger.LogInformation("{0}: {1} rows in database, {2} rows from Genesys. Add {3}, Update {4} and remove {5} from database.",
                nameof(teamDetails),
                teamDetails.Rows.Count,
                TeamsData.Rows.Count,
                numAdded,
                numUpdated,
                numRemoved
                );

            if (numAdded > 0 || numRemoved > 0 || numUpdated> 0)
                DBAdapter.WriteSQLDataBulk(DBUtils.DBUtils.BulkOperationActionType.Save, teamDetails);

            // Process team membership
            numAdded = 0;
            numRemoved = 0;

            // Load team members from database
            using DataTable? teamMemberData = db.GetSQLTableData(
                "SELECT * FROM " + nameof(teamMemberData),
                nameof(teamMemberData)
            );
            if (teamMemberData == null)
                throw new DataException($"Failed to retrieve the {nameof(teamMemberData)} table from the database");

            // Obtain team members from Genesys
            DataTable TeamMembers = GCFact.TeamMembers(TeamsData);

            // Populate a dictionary of keys for quick comparison
            dbKeyToRow = new();
            gcKeyToRow = new();
            foreach(DataRow row in teamMemberData.Rows)
            {
                string? rowId = row.Field<string>("keyid");
                if (string.IsNullOrEmpty(rowId))
                    throw new InvalidDataException($"Field 'keyid' in table {nameof(teamMemberData)} is null or empty");
                dbKeyToRow.Add(rowId, row);
            }
            // Loop through all records from Genesys
            foreach(DataRow row in TeamMembers.Rows)
            {
                // If the database is missing this row, add it.
                string? rowId = row.Field<string>("keyid");
                if (string.IsNullOrEmpty(rowId))
                    throw new InvalidDataException($"Field 'keyid' from Genesys is null or empty for table {nameof(teamDetails)}");
                if (! dbKeyToRow.ContainsKey(rowId))
                {
                    teamMemberData.ImportRow(row);
                    numAdded++;
                    continue;
                }

                gcKeyToRow.Add(rowId, row);
            }
            // Loop through all records from the database
            foreach(string keyid in dbKeyToRow.Keys)
            {
                // If Genesys is missing this row, delete it from the database.
                if (! gcKeyToRow.ContainsKey(keyid))
                {
                    dbKeyToRow[keyid].Delete();
                    numRemoved++;
                }
            }

            _logger.LogInformation("{0}: {1} rows in database, {2} rows from Genesys. Add {3} and remove {4} from database.",
                nameof(teamMemberData),
                teamMemberData.Rows.Count,
                TeamMembers.Rows.Count,
                numAdded,
                numRemoved);

            if (numAdded > 0 || numRemoved > 0)
            {
                string deleteQuery = $"DELETE FROM {nameof(teamMemberData)}";
                DBAdapter.ExecuteSqlNonQuery(deleteQuery);

                DBAdapter.WriteSQLDataBulkWithDeletion(TeamMembers, nameof(teamMemberData));
            }
            else
            {
                DBAdapter.WriteSQLDataBulk(DBUtils.DBUtils.BulkOperationActionType.Save, teamMemberData);
            }

            return true;
        }

        private bool PlanningGroupDetails()
        {
            bool Successful = true;
            DataTable BusUnits = GCFact.BUDetails();
            if (BusUnits != null && BusUnits.Rows.Count > 0)
            {
                DataTable PlanningGroups = GCFact.PlanningGroups(BusUnits);

                if (PlanningGroups != null && PlanningGroups.Rows.Count > 0)
                {
                    Successful = DBAdapter.WriteSQLData(PlanningGroups, "planninggroupdetails");
                    UpdateLastRefreshed(Successful, "planninggroupdetails");
                }

            }
            return Successful;
        }

        private bool ServiceGoalDetails()
        {
            bool Successful = true;
            try
            {
                DataTable BusUnits = GCFact.BUDetails();
                if (BusUnits != null && BusUnits.Rows.Count > 0)
                {
                    DataTable ServiceGoals = GCFact.ServiceGoals(BusUnits);

                    if (ServiceGoals != null && ServiceGoals.Rows.Count > 0)
                    {
                        Successful = DBAdapter.WriteSQLData(ServiceGoals, "servicegoaldetails");
                        UpdateLastRefreshed(Successful, "servicegoaldetails");
                        _logger?.LogInformation("Successfully processed service goals for {ServiceGoalCount} records", ServiceGoals.Rows.Count);
                    }
                    else
                    {
                        _logger?.LogInformation("No service goals data retrieved - this may be due to permission restrictions on all business units");
                        // Still consider this successful - empty data due to permissions is acceptable
                        Successful = true;
                        UpdateLastRefreshed(Successful, "servicegoaldetails");
                    }
                }
                else
                {
                    _logger?.LogWarning("No business units found for service goals processing");
                    Successful = false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing service goal details: {ExceptionType}: {Message}", ex.GetType().Name, ex.Message);
                Successful = false;
            }
            return Successful;
        }

        private bool ActivityCodeDetails()
        {
            bool Successful = false;
            DataTable BusUnits = GCFact.BUDetails();
            if (BusUnits != null && BusUnits.Rows.Count > 0)
            {
                DataTable ActCodes = GCFact.ActCodesDetails(BusUnits);
                Successful = DBAdapter.WriteSQLData(ActCodes, "activitycodeDetails");
                UpdateLastRefreshed(Successful, "activitycodedetails");
            }
            return Successful;
        }

        private bool WrapDetails()
        {
            bool Successful = false;
            try
            {
                DataTable Wrapups = GCFact.WrapupDetails();
                Successful = DBAdapter.WriteSQLData(Wrapups, "wrapupDetails");
                UpdateLastRefreshed(Successful, "wrapupdetails");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Wrap Up Error");
            }
            return Successful;
        }
        private bool MUDetails()
        {
            bool Successful = false;
            DataTable ManUnits = GCFact.MUDetails();
            Successful = DBAdapter.WriteSQLData(ManUnits, "muDetails");
            UpdateLastRefreshed(Successful, "mudetails");

            DataTable MUMembers = GCFact.MUMemberDetails();
            Successful = DBAdapter.WriteSQLData(MUMembers, "muMemberData");
            UpdateLastRefreshed(Successful, "mumemberdata");
            return Successful;
        }
        private bool BUDetails()
        {
            bool Successful = false;
            DataTable BusUnits = GCFact.BUDetails();
            Successful = DBAdapter.WriteSQLData(BusUnits, "buDetails");
            UpdateLastRefreshed(Successful, "budetails");

            return Successful;
        }

        private bool EvaluationDetails()
        {
            bool Successful = false;
            DataTable  Evaluations = GCFact.EvalDetails();
            Successful = DBAdapter.WriteSQLData(Evaluations, "evalDetails");
            UpdateLastRefreshed(Successful, "evaldetails");

            return Successful;

        }
        private bool PresenceDetails()
        {
            bool Successful = false;

            try
            {
                DataTable Presence = GCFact.PresenceDetails();
                Successful = DBAdapter.WriteSQLData(Presence, "presenceDetails");
                UpdateLastRefreshed(Successful, "presencedetails");
            }
            catch (Exception ex)
            {
                Successful = false;
                _logger.LogWarning(ex, "Presence error");
            }

            return Successful;
        }
        private bool QueueDetails()
        {
            bool Successful = false;

            try
            {
                DataTable Queues = GCFact.QueueDetails();
                Successful = DBAdapter.WriteSQLData(Queues, "queueDetails");
                UpdateLastRefreshed(Successful, "queuedetails");
            }
            catch (Exception ex)
            {
                Successful=false;
                _logger.LogWarning(ex, "Queue error");
            }

            return Successful;
        }
        private bool Groups()
        {
            bool Successful = false;

            DataTable Groups = GCFact.GroupDetails();
            Successful = DBAdapter.WriteSQLData(Groups, "groupDetails");
            UpdateLastRefreshed(Successful, "groupdetails");
            DataTable GroupMembership = GCFact.GroupMembershipDetails(Groups);
            Successful = DBAdapter.WriteSQLDataBulkWithDeletion(GroupMembership, "usergroupMappings");
            UpdateLastRefreshed(Successful, "usergroupmappings");

            return Successful;
        }
        private async Task<bool> UserDetails()
        {
            bool Successful = false;

            try
            {
                DataTable Users = await GCFact.UserDetails();
                Successful = DBAdapter.WriteSQLData(Users, "userdetails");
                UpdateLastRefreshed(Successful, "userdetails");
            }
            catch (Exception ex)
            {
                Successful=false;
                _logger.LogWarning(ex, "UserDetails error");
            }

            return Successful;
        }

        private bool DivisionDetails()
        {
            bool Successful = false;
            DataTable Divisions = GCFact.DivisionDetails();
            Successful = DBAdapter.WriteSQLData(Divisions, "divisiondetails");
            UpdateLastRefreshed(Successful, "divisiondetails");

            return Successful;
        }

        public Boolean UpdateODDetails()
        {
            Boolean Successful = false;

            DBAdapter.Initialize();

            GCFact.Initialize();

            GCFactData.GCFactData GCODFact = new GCFactData.GCFactData();

            DataTable ContactLists = GCODFact.ODContactLists();

            Successful = DBAdapter.WriteSQLData(ContactLists, "odcontactlistdetails");
            UpdateLastRefreshed(Successful, "odcontactlistdetails");

            DataTable Campaigns = GCODFact.ODCampaignDetails();

            Successful = DBAdapter.WriteSQLData(Campaigns, "odcampaigndetails");
            UpdateLastRefreshed(Successful, "odcampaigndetails");

            return Successful;
        }

        public async Task<Boolean> UpdateGCUserQueueMapping()
        {
            bool Successful = false;

            DBAdapter.Initialize();

            GCFact.Initialize();

            DataTable UserQueues = await GCFact.UserQueueMappings();
            var idsToKeep = new List<string>();
            foreach (DataRow row in UserQueues.Rows)
            {
                string id = Convert.ToString(row["KEYID"]);
                idsToKeep.Add("'" + id.Replace("'", "''") + "'");
            }

            if (idsToKeep.Count > 0)
            {
                string ids = string.Join(",", idsToKeep);
                string deleteQuery = $"DELETE FROM userqueuemappings WHERE keyid NOT IN ({ids})";
                DBAdapter.ExecuteSqlNonQuery(deleteQuery);
            }
            Successful = DBAdapter.WriteSQLData(UserQueues, "userqueuemappings");

            UpdateLastRefreshed(Successful, "userqueuemappings");
            return Successful;
        }

        public Boolean LearningDataDetails()
        {
            bool successful = false;
            bool permissionErrorEncountered = false;
            bool criticalErrorEncountered = false;

            DBAdapter.Initialize();
            GCFact.Initialize();

            try
            {
                // Step 1: Get Learning Modules
                try
                {
                    DataTable learningModules = GCFact.LearningModuleDetails();

                    // If we got data, write it to the database
                    if (learningModules != null && learningModules.Rows.Count > 0)
                    {
                        successful = DBAdapter.WriteSQLData(learningModules, "learningmodules");
                        if (successful)
                        {
                            UpdateLastRefreshed(successful, "learningmodules");
                            _logger.LogInformation("Successfully wrote {Count} learning modules to database", learningModules.Rows.Count);
                        }
                        else
                        {
                            _logger.LogError("Failed to write learning modules to database");
                            criticalErrorEncountered = true;
                        }

                        // Only proceed with assignments if we successfully wrote modules
                        if (successful)
                        {
                            // Step 2: Get Learning Module Assignments if we have modules
                            try
                            {
                                DataTable learningModulesAssignmentsData = GCFact.LearningModuleAssignmentsDetails(learningModules);

                                // If we got data, write it to the database
                                if (learningModulesAssignmentsData != null && learningModulesAssignmentsData.Rows.Count > 0)
                                {
                                    successful = DBAdapter.WriteSQLData(learningModulesAssignmentsData, "learningmoduleassignments");
                                    if (successful)
                                    {
                                        UpdateLastRefreshed(successful, "learningmoduleassignments");
                                        _logger.LogInformation("Successfully wrote {Count} learning module assignments to database",
                                            learningModulesAssignmentsData.Rows.Count);
                                    }
                                    else
                                    {
                                        _logger.LogError("Failed to write learning module assignments to database");
                                        criticalErrorEncountered = true;
                                    }
                                }
                                else
                                {
                                    _logger.LogInformation("No learning module assignments data to write to database");
                                    // Still consider this successful - empty data is valid
                                    successful = true;
                                }
                            }
                            catch (UnauthorizedAccessException uaEx)
                            {
                                // Permission error for assignments - log but continue
                                _logger.LogWarning(uaEx, "Permission denied for learning module assignments. This feature will be skipped but processing will continue.");
                                permissionErrorEncountered = true;
                                // We still consider the job successful if we got modules but not assignments due to permissions
                                successful = true;
                            }
                            catch (InvalidOperationException ioEx) when (ioEx.Message.Contains(PERMISSION_DENIED_TEXT) ||
                                                                        (ioEx.InnerException != null && ioEx.InnerException.Message.Contains(PERMISSION_DENIED_TEXT)))
                            {
                                // Handle wrapped permission errors
                                _logger.LogWarning(ioEx, "Permission denied for learning module assignments. This feature will be skipped but processing will continue.");
                                permissionErrorEncountered = true;
                                successful = true;
                            }
                            catch (Exception ex)
                            {
                                // Critical error for assignments
                                _logger.LogError(ex, "Critical error processing learning module assignments: {ExceptionType}: {Message}",
                                    ex.GetType().Name, ex.Message);
                                criticalErrorEncountered = true;
                            }
                        }
                    }
                    else
                    {
                        _logger.LogInformation("No learning modules data to write to database");
                        // Still consider this successful - empty data is valid
                        successful = true;
                    }
                }
                catch (UnauthorizedAccessException uaEx)
                {
                    // Permission error for modules - log but continue
                    _logger.LogWarning(uaEx, "Permission denied for learning modules. This feature will be skipped but processing will continue.");
                    permissionErrorEncountered = true;
                    // We still consider the job successful if we couldn't get modules due to permissions
                    successful = true;
                }
                catch (InvalidOperationException ioEx) when (ioEx.Message.Contains(PERMISSION_DENIED_TEXT) ||
                                                           (ioEx.InnerException != null && ioEx.InnerException.Message.Contains(PERMISSION_DENIED_TEXT)))
                {
                    // Handle wrapped permission errors
                    _logger.LogWarning(ioEx, "Permission denied for learning modules. This feature will be skipped but processing will continue.");
                    permissionErrorEncountered = true;
                    successful = true;
                }
                catch (Exception ex)
                {
                    // Critical error for modules
                    _logger.LogError(ex, "Critical error processing learning modules: {ExceptionType}: {Message}",
                        ex.GetType().Name, ex.Message);
                    criticalErrorEncountered = true;
                }
            }
            catch (Exception ex)
            {
                // Unexpected error at the top level
                _logger.LogError(ex, "Unexpected error in LearningDataDetails: {ExceptionType}: {Message}",
                    ex.GetType().Name, ex.Message);
                criticalErrorEncountered = true;
            }

            // Log summary based on what happened
            if (criticalErrorEncountered)
            {
                _logger.LogError("Learning data job failed due to critical errors");
                return false; // Only return false for critical errors
            }
            else if (permissionErrorEncountered)
            {
                _logger.LogInformation("Learning data job completed with permission errors. Some features were skipped but processing continued successfully.");
                return true; // Return true for permission errors - don't fail the entire FactData job
            }
            else if (successful)
            {
                _logger.LogInformation("Learning data job completed successfully");
            }
            else
            {
                _logger.LogInformation("Learning data job completed with no data processed");
            }

            return true; // Default to true to avoid failing the entire FactData job
        }

        public bool KnowledgeBaseDetails()
        {
            bool Successful = false;
            DBAdapter.Initialize();

            GCFact.Initialize();
            try
            {
                DataTable KnowledgeBase = GCFact.KnowledgeBaseDetails();
                Successful = DBAdapter.WriteSQLData(KnowledgeBase, "knowledgebase");
                UpdateLastRefreshed(Successful, "knowledgebase");

                DataTable KnowledgeBaseCategoryData = GCFact.KnowledgeBaseCategoryDetails(KnowledgeBase);
                Successful = DBAdapter.WriteSQLData(KnowledgeBaseCategoryData, "knowledgebasecategorydata");
                UpdateLastRefreshed(Successful, "knowledgebasecategorydata");

            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Knowledge Base Error");
            }
            return Successful;
        }

        public bool FlowOutcomeDetails()
        {
            bool Successful = false;
            DBAdapter.Initialize();

            GCFact.Initialize();
            try
            {
                DataTable FlowOutcomes = GCFact.FlowOutcomeDetails();
                Successful = DBAdapter.WriteSQLData(FlowOutcomes, "flowoutcomedetails");
                UpdateLastRefreshed(Successful, "flowoutcomedetails");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Flow Outcome Details Error");
            }
            return Successful;
        }

        private bool ScheduleDetails()
        {
            bool Successful = true;
            string SyncType = "scheduledetails";

            try
            {
                DBUtils.DBUtils DBAdapterLocal = new DBUtils.DBUtils();
                DBAdapterLocal.Initialize();

                GCGetData GCData = new GCGetData(_logger);
                GCData.Initialize(SyncType);

                DataTable ScheduleDetailsData = GCData.WFMScheduleDetails();

                if (ScheduleDetailsData.Rows.Count > 0)
                {
                    Successful = DBAdapterLocal.WriteSQLData(ScheduleDetailsData, "scheduledetails");
                    _logger?.LogInformation("Successfully processed {Count} schedule details records", ScheduleDetailsData.Rows.Count);
                }
                else
                {
                    _logger?.LogInformation("Schedule details: No rows to update");
                }

                if (Successful)
                {
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "scheduledetails");
                }
                else
                {
                    _logger?.LogWarning("Will not update the last update date for Schedule Details Data - failure in processing");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing schedule details: {ExceptionType}: {Message}", ex.GetType().Name, ex.Message);
                Successful = false;
            }

            return Successful;
        }

        private void UpdateLastRefreshed(bool success, string syncType)
        {
            if (success)
            {
                GCFact.UpdateLastSuccessDate(syncType);
            }
            else
            {
                _logger.LogInformation("Will not update the last update date for {SyncType} - failure in processing", syncType);
            }
        }

    }
}
// spell-checker: ignore: mudetails, wrapupdetails
